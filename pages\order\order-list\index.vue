<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<scroll-view scroll-x class="nav  custom-nav">
			<view class="flex text-center">
				<view :class="'cu-item flex-sub ' + (index==tabCur?'cur-custom':'')"
				      :style="index==tabCur ? 'color: #363636' : 'color: #979797'"
				      v-for="(item, index) in orderStatus"
				      :key="index" @tap="tabSelect" :data-index="index" :data-key="item.key">{{item.value}}</view>
			</view>
		</scroll-view>
		<view class="">
			<!-- 未登录状态显示 -->
			<view v-if="!isLoggedIn" class="empty-state">
				<view class="empty-content">
					<view class="empty-icon">
						<text class="cuIcon-order text-gray" style="font-size: 120rpx;"></text>
					</view>
					<view class="empty-text">
						<text class="text-gray text-lg">此处显示您的订单信息</text>
					</view>
					<view class="empty-subtext">
						<text class="text-gray">您还未登录</text>
					</view>
					<view class="empty-action">
						<button class="cu-btn bg-blue lg round margin-top" @tap="goToLogin">去登录</button>
					</view>
				</view>
			</view>

			<!-- 已登录状态显示订单列表 -->
			<view v-else class="cu-card article" >
				<view class="cu-item" v-for="(item, index) in orderList" :key="index">
					<view class="bg-white flex justify-end padding-lr padding-top padding-bottom-xs">
<!--						<navigator hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + item.shopInfo.id">
							<view class="cu-avatar sm radius" :style="'background-image:url(' + item.shopInfo.imgUrl + ')'"></view>
							<text class="text-black text-bold margin-left-sm">{{item.shopInfo.name}}</text>
							<text class="cuIcon-right text-sm"></text>
						</navigator>-->
						<view class="text-red text-sm">{{item.statusDesc}}</view>
					</view>

					<navigator hover-class="none" :url="'/pages/order/order-detail/index?id=' + item.id" class="cu-item" v-for="(item2, index2) in item.listOrderItem"
					 :key="index2">
						<view class="content align-center">
							<image :src="item2.picUrl ? item2.picUrl : '/static/public/img/no_pic.png'" mode="aspectFill" class="row-img margin-top-xs"></image>
							<view class="desc row-info block">
								<view class="text-black margin-top-xs overflow-2">
									<text class="cu-tag bg-red sm radius margin-right-xs" v-if="item.orderType != '0'">{{item.orderType == '1' ? '砍价' : item.orderType == '2' ? '拼团' : item.orderType == '3' ? '秒杀' : ''}}</text>
									<text class="text-df">{{item2.spuName}}</text></view>
								<view class="text-gray text-sm overflow-2 margin-top-xs" v-if="item2.specInfo">{{item2.specInfo}}</view>
								<view class="flex justify-between align-center margin-top-xs">
									<view class="text-price text-xl text-bold text-red">{{item2.paymentPrice}}</view>
									<view class="text-gray text-l">x{{item2.quantity}}</view>
								</view>	
								<view class="margin-top-xs flex justify-between align-center">
									<text class="flex-sub text-sm text-gray">创建时间:</text>
									<view class="text-sm text-gray">{{item2.createTime}}</view>
								</view>
							</view>
						</view>
						<view class="cu-item text-right padding-sm margin-right-sm" @tap.stop>
							<navigator class="cu-btn line sm text-orange" v-if="item2.status != '0'" :url="'/pages/order/order-refunds/form/index?orderItemId=' + item2.id">{{item2.statusDesc}}</navigator>
						</view>
						
					</navigator>
					
					<!-- 预约场地信息展开折叠块 -->
					<view v-if="item.auctionInfo" class="bg-white margin-lr padding-lr margin-bottom-sm">
						<view class="auction-info-header flex align-center justify-between padding-tb-sm"
							  @tap="toggleAuctionInfo(index)">
							<view class="flex align-center">
								<text class="cuIcon-locationfill text-blue margin-right-xs"></text>
								<text class="text-bold text-df">预约场地信息</text>
							</view>
							<text :class="'cuIcon-' + (item.showAuctionInfo ? 'unfold' : 'fold') + ' text-gray'"></text>
						</view>
						<view v-if="item.showAuctionInfo" class="auction-info-content padding-lr">
							<view class="auction-info-item flex align-center">
								<text class="auction-label">拍摄门店：</text>
								<text class="auction-value text-black">{{item.auctionInfo.shopName}}</text>
							</view>
							
							<view class="auction-info-item flex align-center">
								<text class="auction-label">到店时间：</text>
								<text class="auction-value text-black">{{item.auctionInfo.startTime}}</text>
							</view>
						</view>
					</view>
					<order-operate class="response" :orderInfo="item"
					 @orderCancel="orderCancel($event,index)"
					 @orderReceive="orderCancel($event,index)"
					 @orderDel="orderDel($event,index)"
					 @unifiedOrder="unifiedOrder($event,index)"
					 @inviteFriends="inviteFriends($event,index)"
					 :inviteLoading="inviteLoading"
					 :data-index="index">
					</order-operate>
				</view>
				<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
			</view>
		</view>

		<!-- 邀请好友确认对话框 -->
		<view v-if="showShareDialog" class="share-mask" @tap="closeShareDialog">
			<view class="share-dialog" @tap.stop>
				<view class="share-title">{{inviteDialogContent}}</view>
				<view class="share-buttons">
					<button class="share-btn cancel-btn" @tap="closeShareDialog">取消</button>
					<button class="share-btn confirm-btn" open-type="share">确认分享</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import orderOperate from "components/order-operate/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				isLoggedIn: false, // 登录状态
				tabCur: 0,
				orderStatus: [{
					value: '全部',
					key: ''
				}, /* {
					value: '待付款',
					key: '0'
				}, */ {
					value: '待拍摄',
					key: '12'
				}, /*{
					value: '待约档',
					key: '2'
				}, */{
					value: '已完成',
					key: '3'
				}],
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				orderList: [],
				// 邀请好友相关数据
				showShareDialog: false,
				inviteDialogContent: '要邀请好友参与拼团活动吗？',
				currentShareOrder: null, // 当前要分享的订单
				pageDivData: null, // 页面分享数据
				inviteLoading: false // 邀请好友按钮加载状态
			};
		},

		components: {
			orderOperate
		},
		props: {},

		onShow() {
			// 检查登录状态
			this.isLoggedIn = !!uni.getStorageSync('third_session');

			app.initPage().then(res => {
				// 只有已登录才请求订单数据
				if (this.isLoggedIn) {
					this.loadmore = true;
					this.orderList = [];
					this.page.current = 1;
					this.orderPage();
				}
			});
		},
		onLoad: function(options) {
			if (options.status || options.status == 0) {
				let that = this;
				this.parameter.status = options.status;
				this.orderStatus.forEach(function(status, index) {
					if (status.key == options.status) {
						that.tabCur = index;
					}
				});
			}
		},

		onReachBottom() {
			// 未登录时不处理滚动加载
			if (this.loadmore && this.isLoggedIn) {
				this.page.current = this.page.current + 1;
				this.orderPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},

		methods: {
			// 跳转到登录页面
			goToLogin() {
				uni.navigateTo({
					url: '/pages/login/wechat-auth'
				});
			},

			orderPage() {
				// 未登录时不请求接口
				if (!this.isLoggedIn) {
					return;
				}

				api.orderPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let orderList = res.data.records;
					// 为每个订单添加展开状态属性
					orderList.forEach(order => {
						// 待拍摄状态(status='12')默认展开，其他状态默认关闭
						order.showAuctionInfo = order.status === '12';
					});
					this.orderList = [...this.orderList, ...orderList];
					if (orderList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			/**
			 * 切换预约场地信息展开/折叠状态
			 */
			toggleAuctionInfo(index) {
				this.orderList[index].showAuctionInfo = !this.orderList[index].showAuctionInfo;
				this.$forceUpdate(); // 强制更新视图
			},

			/**
			 * 格式化预约时间显示
			 */
			formatAuctionTime(startTime, endTime) {
				if (!startTime || !endTime) {
					return '';
				}

				// 提取日期和时间部分
				const startDate = startTime.split(' ')[0];
				const startTimeOnly = startTime.split(' ')[1];
				const endTimeOnly = endTime.split(' ')[1];

				return `${startDate} ${startTimeOnly}-${endTimeOnly}`;
			},

			refresh() {
				// 未登录时不刷新数据
				if (!this.isLoggedIn) {
					return;
				}

				this.loadmore = true;
				this.orderList = [];
				this.page.current = 1;
				this.orderPage();
			},

			tabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					this.parameter.status = dataset.key;
					this.refresh();
				}
			},

			orderCancel(item, index) {
				api.orderGet(this.orderList[index].id).then(res => {
					this.orderList[index] = res.data;
					this.orderList.splice(); //当页面不渲染时调用此方法可以刷新页面
				});

			},

			orderDel(item, index) {
				this.orderList.splice(index, 1);
			},

			unifiedOrder(item, index) {
				let orderList = this.orderList;
				api.orderGet(orderList[index].id).then(res => {
					this.orderList[index] = res.data;
				});
			},

			/**
			 * 邀请好友事件处理
			 */
			inviteFriends(event, index) {
				const orderInfo = this.orderList[index];
				console.log("邀请好友，订单信息:", orderInfo);

				// 设置加载状态
				this.inviteLoading = true;

				// 保存当前要分享的订单
				this.currentShareOrder = orderInfo;

				// 更新分享参数并获取分享数据
				this.updateShareParams(orderInfo);
			},

			/**
			 * 更新分享参数并获取分享数据
			 */
			updateShareParams(orderInfo) {
				// 准备页面设计API参数
				const pageParams = {
					id: orderInfo.pageId,
					tenantid: orderInfo.tenantId
				};

				// 准备拼团API参数
				const groupParams = {
					pageId: orderInfo.pageId,
					groupId: null
				};

				console.log("获取页面分享数据, 页面参数:", pageParams);
				console.log("获取拼团数据, 拼团参数:", groupParams);

				// 并发调用两个API
				Promise.all([
					api.pagedevise(pageParams),
					api.getSpellGroup(groupParams)
				])
					.then(([pageRes, groupRes]) => {
						console.log("页面设计数据获取成功:", pageRes);
						console.log("拼团数据获取成功:", groupRes);

						// 处理页面设计数据
						this.pageDivData = pageRes.data;

						// 从拼团数据中获取groupId并更新currentShareOrder
						if (groupRes.data && groupRes.data.groupUserList && groupRes.data.groupUserList.length > 0) {
							this.currentShareOrder.groupId = groupRes.data.groupUserList[0].groupId;
							console.log("获取到groupId:", this.currentShareOrder.groupId);
						}

						// 更新对话框内容
						if (this.pageDivData && this.pageDivData.pageBase && this.pageDivData.pageBase.shareTitle) {
							this.inviteDialogContent = `要邀请好友参与${this.pageDivData.pageBase.shareTitle}活动吗？`;
						}

						// 显示分享对话框
						this.showShareDialog = true;

						// 恢复按钮状态
						this.inviteLoading = false;
					})
					.catch(err => {
						console.error("获取分享数据失败:", err);
						// 即使获取失败也显示默认对话框
						this.showShareDialog = true;

						// 恢复按钮状态
						this.inviteLoading = false;
					});
			},

			/**
			 * 关闭分享对话框
			 */
			closeShareDialog() {
				this.showShareDialog = false;
				this.currentShareOrder = null;
				this.inviteLoading = false; // 恢复按钮状态
			}

		},

		/**
		 * 分享功能配置
		 */
		onShareAppMessage() {
			// 如果没有当前分享订单，返回默认配置
			if (!this.currentShareOrder) {
				return {
					title: '邀请您参与拼团',
					path: 'pages/groupon/index'
				};
			}

			// 如果没有分享数据，返回默认配置
			if (!this.pageDivData || !this.pageDivData.pageBase) {
				return {
					title: '邀请您参与拼团',
					path: 'pages/groupon/index'
				};
			}

			// 获取分享标题、描述和图片URL
			let title = this.pageDivData.pageBase.shareTitle || '邀请您参与拼团';
			let imageUrl = this.pageDivData.pageBase.shareImgUrl || '';

			// 构建当前页面路径
			let path = 'pages/groupon/index';

			// 添加当前页面的查询参数
			const query = [];
			if (this.currentShareOrder.pageId) query.push('page_id=' + this.currentShareOrder.pageId);
			if (this.currentShareOrder.appId) query.push('app_id=' + this.currentShareOrder.appId);
			if (this.currentShareOrder.tenantId) query.push('tenant_id=' + this.currentShareOrder.tenantId);
			if (this.currentShareOrder.groupId) query.push('group_id=' + this.currentShareOrder.groupId);

			// 添加用户标识
			const userInfo = uni.getStorageSync('user_info');
			if (userInfo && userInfo.id) {
				query.push('sharer_friend_id=' + userInfo.id);
			}

			// 拼接查询参数
			if (query.length > 0) {
				path = path + '?' + query.join('&');
			}

			console.log('分享配置:', { title, path, imageUrl });

			return {
				title: title,
				path: path,
				imageUrl: imageUrl,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log('分享成功:', res.errMsg);
					}
				},
				fail: function(res) {
					console.log('分享失败:', res);
				}
			};
		}
	};
</script>
<style scoped>
	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx
	}

	.nav {
		top: unset !important;
	}

	/* 自定义导航样式 - 无背景边框 */
	.custom-nav {
		background: transparent !important;
	}

	.custom-nav .cu-item {
		border-bottom: none !important;
		background: transparent !important;
	}

	.custom-nav .cu-item.cur-custom {
		border-bottom: none !important;
		background: transparent !important;
	}

	/* 预约场地信息样式 */
	.auction-info-header {
		border-top: 1px solid #f0f0f0;
		cursor: pointer;
		transition: background-color 0.2s;
	}

	.auction-info-header:active {
		background-color: #f8f8f8;
	}

	.auction-info-content {
		border-bottom: 1px solid #f0f0f0;
		background-color: #fafafa;
	}

	.auction-info-item {
		padding: 8rpx 0;
	}

	.auction-label {
		color: #666;
		font-size: 28rpx;
		width: 140rpx;
		flex-shrink: 0;
	}

	.auction-value {
		color: #333;
		font-size: 28rpx;
		flex: 1;
	}

	/* 分享对话框遮罩 */
	.share-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	/* 分享对话框 */
	.share-dialog {
		background: white;
		border-radius: 10px;
		padding: 30px;
		width: 280px;
		text-align: center;
		margin: 0 20px;
	}

	/* 对话框标题 */
	.share-title {
		font-size: 16px;
		color: #333;
		margin-bottom: 30px;
		line-height: 1.5;
	}

	/* 按钮容器 */
	.share-buttons {
		display: flex;
		justify-content: space-between;
		gap: 15px;
	}

	/* 按钮样式 */
	.share-btn {
		flex: 1;
		height: 40px;
		border-radius: 20px;
		border: none;
		font-size: 14px;
		cursor: pointer;
		line-height: 40px;
	}

	/* 取消按钮 */
	.cancel-btn {
		background: #f5f5f5;
		color: #666;
	}

	/* 确认按钮 */
	.confirm-btn {
		background: #007aff;
		color: white;
	}

	/* 移除按钮默认边框 */
	.confirm-btn::after,
	.cancel-btn::after {
		border: none;
	}

	/* 空白状态样式 */
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 60vh;
		padding: 40rpx;
	}

	.empty-content {
		text-align: center;
		max-width: 400rpx;
	}

	.empty-icon {
		margin-bottom: 30rpx;
	}

	.empty-text {
		margin-bottom: 15rpx;
	}

	.empty-subtext {
		margin-bottom: 40rpx;
	}

	.empty-action {
		margin-top: 30rpx;
	}
</style>
